# 用户数据清理功能说明

## 概述

在 `UnifyUpdateServiceImpl.java` 文件的 `updateAllUser` 方法中添加了新的处理逻辑，用于清理不存在的用户数据，确保数据一致性。

## 功能描述

### 主要功能
1. 获取当前 `List<EmployeeInfo> employeeInfos` 中所有在职用户的 userid 列表
2. 查询 `UnifyUserNewExtendInfo` 表中与当前 `user_dept_id` 相关的所有数据
3. 比较这两个数据集，找出在 `UnifyUserNewExtendInfo` 中存在但不在 `employeeInfos` 中的 userid
4. 删除 `UnifyUserNewExtendInfo` 表中这些不存在的用户数据
5. 基于删除操作，重新处理和更新 `unify_user_new` 表的相关数据
6. 将更新后的数据推送到所有相关的子系统

### 实现位置
- **文件**: `concise-manage/src/main/java/com/concise/unify/service/impl/UnifyUpdateServiceImpl.java`
- **主方法**: `updateAllUser(String organizationCode, List<UnifyDepartHalf> unifyDepartHalfList)`
- **新增方法**: 
  - `cleanupInactiveUsers(String organizationCode, List<String> activeUserIds)`
  - `updateUserNewDataAfterCleanup(List<String> inactiveUserIds)`
  - `pushUpdatedDataToSystems(List<String> inactiveUserIds)`

## 技术实现

### 1. 数据收集阶段
```java
// 在处理每个在职用户时收集用户ID
List<String> activeUserIds = new ArrayList<>();
for (EmployeeInfo employeeInfo : employeeInfos) {
    activeUserIds.add(employeeInfo.getEmployeeCode());
    // ... 原有处理逻辑
}
```

### 2. 清理逻辑
```java
// 查询当前组织的所有扩展信息
List<UnifyUserNewExtendInfo> allExtendInfos = unifyUserNewExtendInfoService.list(
    new QueryWrapper<UnifyUserNewExtendInfo>().lambda()
        .eq(UnifyUserNewExtendInfo::getUserDeptId, organizationCode)
);

// 找出不活跃的用户
List<String> inactiveUserIds = allExtendInfos.stream()
    .map(UnifyUserNewExtendInfo::getUserId)
    .filter(userId -> !activeUserIds.contains(userId))
    .distinct()
    .collect(Collectors.toList());
```

### 3. 数据删除
```java
// 删除扩展信息表中的不活跃用户数据
for (String inactiveUserId : inactiveUserIds) {
    unifyUserNewExtendInfoService.remove(
        new QueryWrapper<UnifyUserNewExtendInfo>().lambda()
            .eq(UnifyUserNewExtendInfo::getUserId, inactiveUserId)
            .eq(UnifyUserNewExtendInfo::getUserDeptId, organizationCode)
    );
}
```

### 4. 数据更新
```java
// 重新构建用户的JSON信息
for (String userId : inactiveUserIds) {
    UnifyUserNew unifyUserNew = unifyUserNewService.getById(userId);
    // 重新查询剩余的扩展信息
    // 更新用户的JSON数据
    // 保存到数据库
}
```

### 5. 数据推送
```java
// 推送更新后的数据到子系统
for (String userId : inactiveUserIds) {
    LxUserVo lxUserVo = // 从数据库获取最新数据
    lxUserVo.setOperatorType(1); // 设置为更新操作
    unifyPushService.handleUser(lxUserVo);
    unifyPushService.handleUserSync(lxUserVo, System.currentTimeMillis());
}
```

## 事务处理

### 事务注解
- `@Transactional(rollbackFor = Exception.class)` 确保数据一致性
- 如果任何步骤失败，整个清理操作会回滚

### 异常处理
- 每个关键步骤都有 try-catch 块
- 详细的日志记录用于问题追踪
- 异常会被重新抛出以触发事务回滚

## 日志记录

### 日志级别
- **INFO**: 关键操作的开始、结束和统计信息
- **DEBUG**: 详细的处理过程和单个记录的操作
- **ERROR**: 异常情况和错误信息

### 日志示例
```
INFO  - 开始清理组织 ORG_001 中不存在的用户数据，在职用户数量: 150
INFO  - 组织 ORG_001 中找到 200 条扩展信息数据
INFO  - 组织 ORG_001 中找到 50 个需要清理的不活跃用户
INFO  - 成功删除 50 条扩展信息记录
INFO  - 完成 50 个用户的 unify_user_new 数据更新
INFO  - 完成 50 个用户的数据推送
INFO  - 组织 ORG_001 的用户数据清理完成
```

## 性能考虑

### 批量处理
- 使用流式处理减少内存占用
- 分批查询和处理大量数据

### 数据库优化
- 使用索引字段进行查询（userId, userDeptId）
- 避免全表扫描

### 异步处理
- 原有的 `@Async` 注解保持不变
- 清理操作在异步线程中执行

## 测试

### 测试文件
- `concise-manage/src/test/java/com/concise/unify/service/impl/UnifyUpdateServiceImplTest.java`

### 测试用例
1. **testCleanupInactiveUsersLogic**: 测试清理逻辑的正确性
2. **testUpdateAllUserMethodStructure**: 验证方法结构
3. **testDataConsistencyCheck**: 验证数据一致性

### 运行测试
```bash
mvn test -Dtest=UnifyUpdateServiceImplTest
```

## 注意事项

### 数据安全
- 删除操作不可逆，请确保在生产环境中谨慎使用
- 建议在执行前备份相关数据

### 性能影响
- 清理操作会增加方法执行时间
- 建议在低峰期执行大规模清理

### 监控建议
- 监控清理操作的执行时间和影响的记录数
- 设置适当的告警阈值

## 配置参数

目前没有额外的配置参数，所有逻辑都集成在现有方法中。如需要可配置的清理策略，可以考虑添加以下配置：

```properties
# 是否启用用户数据清理功能
unify.user.cleanup.enabled=true

# 清理操作的批次大小
unify.user.cleanup.batch-size=100

# 是否在清理后推送数据到子系统
unify.user.cleanup.push-to-systems=true
```

## 版本信息

- **添加日期**: 2024-08-08
- **版本**: 1.0.0
- **作者**: System
- **审核**: 待审核

package com.concise.unify.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.xxpt.gateway.shared.api.response.*;
import com.alibaba.xxpt.gateway.shared.client.http.api.OapiSpResultContent;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.concise.gen.unifyuser.entity.UnifyUser;
import com.concise.gen.unifyuser.service.UnifyUserService;
import com.concise.gen.unifyusernewextendinfo.entity.UnifyUserNewExtendInfo;
import com.concise.gen.unifyusernewextendinfo.service.UnifyUserNewExtendInfoService;
import com.concise.modular.zwdd.ZwddUtil;
import com.concise.modular.zwdd.orgvo.OrgLine;
import com.concise.modular.zwdd.orgvo.Organization;
import com.concise.modular.zwdd.uservo.EmployeeInfo;
import com.concise.modular.zwdd.uservo.EmployeePosition;
import com.concise.unify.depart.LxDepartVo;
import com.concise.unify.enums.OrganizationTypeEnum;
import com.concise.unify.service.UnifyPushService;
import com.concise.unify.service.UnifyUpdateService;
import com.concise.unify.unifydeparthalf.entity.UnifyDepartHalf;
import com.concise.unify.unifydeparthalf.service.UnifyDepartHalfService;
import com.concise.unify.unifydepartnew.entity.UnifyDepartNew;
import com.concise.unify.unifydepartnew.service.UnifyDepartNewService;
import com.concise.unify.unifyusernew.entity.UnifyUserNew;
import com.concise.unify.unifyusernew.service.UnifyUserNewService;
import com.concise.unify.user.LxExtendInfosVo;
import com.concise.unify.user.LxUserVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/7
 */
@Slf4j
@Service
public class UnifyUpdateServiceImpl implements UnifyUpdateService {
    @Resource
    private UnifyDepartNewService unifyDepartNewService;

    @Resource
    private UnifyPushService unifyPushService;

    @Resource
    private UnifyUserNewService unifyUserNewService;

    @Resource
    private UnifyUserService unifyUserService;

    @Resource
    private UnifyDepartHalfService unifyDepartHalfService;

    @Resource
    private UnifyUserNewExtendInfoService unifyUserNewExtendInfoService;

    @Override
    public void updateAllOrgData() {
        List<OrgLine> allOrgLine = new ArrayList<>();
        List<Organization> allOrganization = new ArrayList<>();
//        List<UnifyDepartHalf> departHalfList = unifyDepartHalfService.list();
        //先获取省司法厅和各地市
        List<OrgLine> topOrgLine = getAllOrgLine("LD_d858587bd54945edb6232980ba1549f7", "LD_d858587bd54945edb6232980ba1549f7");
        log.info(JSONObject.toJSONString(topOrgLine));
        if (CollectionUtil.isNotEmpty(topOrgLine)) {
            dealFirstLine(topOrgLine);
            for (OrgLine orgLine : topOrgLine) {
                List<OrgLine> cityOrgLine = getAllOrgLine(orgLine.getDomainCode(), orgLine.getOrganizationCode());
                dealSecondLine(cityOrgLine);
                //省级
                if ("省司法厅".equals(orgLine.getOrganizationName())) {
                    List<Organization> organizationList = getAllOrganizationProvince(orgLine.getRefOrganizationCode());
                    allOrganization.addAll(organizationList);
                }
                //地市
                if (CollectionUtil.isNotEmpty(cityOrgLine)) {
                    for (OrgLine line : cityOrgLine) {
                        if (ObjectUtil.isNotEmpty(line.getRefOrganizationCode())) {
                            List<Organization> organizationList = getAllOrganization(line.getRefOrganizationCode());
                            if (CollectionUtil.isNotEmpty(organizationList)) {
                                allOrganization.addAll(organizationList);

                            }

                        }
                    }
                }
            }
        }

        dealOrganization(allOrganization);
        log.debug(JSONObject.toJSONString(allOrgLine));
        log.debug(JSONObject.toJSONString(allOrganization));

    }

    private List<Organization> getAllOrganizationProvince(String organizationCode) {
        List<String> orgIds = new ArrayList<>();
        List<Organization> organizationList = new ArrayList<>();
        OapiMoziOrganizationPageSubOrganizationCodesResponse response = ZwddUtil.pageSubOrganizationCodes(1, organizationCode);
        if (response.getContent() != null) {
            Long totalSize = response.getContent().getTotalSize();
            String data = response.getContent().getData();
            List<String> stringList = JSONObject.parseArray(data, String.class);
            if (CollectionUtil.isNotEmpty(stringList)) {
                orgIds.addAll(stringList);
                if (totalSize > 100) {
                    int t = (int) (totalSize / 2);
                    for (int l = 0; l < t + 1; l++) {
                        OapiMoziOrganizationPageSubOrganizationCodesResponse responsePage = ZwddUtil.pageSubOrganizationCodes(l + 2, organizationCode);
                        if (responsePage.getContent() != null && responsePage.getContent().getData() != null) {
                            String dataPage = responsePage.getContent().getData();
                            List<String> lineListPage = JSONObject.parseArray(dataPage, String.class);
                            orgIds.addAll(lineListPage);
                        }
                    }
                }
            }

        }
        if (!orgIds.isEmpty()) {

            List<Organization> organizationsByCodes = ZwddUtil.listOrganizationsByCodes(orgIds);
            if (!organizationsByCodes.isEmpty()) {
                for (Organization organizationsByCode : organizationsByCodes) {
                    //此处排除了省司法厅下属单位及下面的机构，这里2万人，数据太多，暂不同步
                    if (!organizationsByCode.isLeaf() && !"GO_9326af6fd14e41d6b0d6b86d6c4f52d5".equals(organizationsByCode.getOrganizationCode())) {
                        organizationList.add(organizationsByCode);
                        List<Organization> childList = getAllOrganization(organizationsByCode.getOrganizationCode());
                        if (CollectionUtil.isNotEmpty(childList)) {
                            organizationList.addAll(childList);
                        }
                    } else {
                        organizationList.add(organizationsByCode);
                    }
                }
            }

        }
        return organizationList;
    }

    @Override
    public void updateAllDataForUser() {

        List<UnifyDepartHalf> departHalfList = unifyDepartHalfService.list();
        //先获取省司法厅和各地市
        List<OrgLine> topOrgLine = getAllOrgLine("LD_d858587bd54945edb6232980ba1549f7", "LD_d858587bd54945edb6232980ba1549f7");
        log.info(JSONObject.toJSONString(topOrgLine));
        if (CollectionUtil.isNotEmpty(topOrgLine)) {
            dealFirstLine(topOrgLine);
            for (OrgLine orgLine : topOrgLine) {
                List<OrgLine> cityOrgLine = getAllOrgLine(orgLine.getDomainCode(), orgLine.getOrganizationCode());
                dealSecondLine(cityOrgLine);
                //省级
                if ("省司法厅".equals(orgLine.getOrganizationName())) {
                    List<Organization> organizationList = getAllOrganizationProvince(orgLine.getRefOrganizationCode());
                    for (Organization organization : organizationList) {
                        if (!"下属单位".equals(organization.getOrganizationName())) {
                            updateAllUser(organization.getOrganizationCode(), departHalfList);
                        }
                    }
                }
                //地市
                if (CollectionUtil.isNotEmpty(cityOrgLine)) {
                    for (OrgLine line : cityOrgLine) {
                        if (ObjectUtil.isNotEmpty(line.getRefOrganizationCode())) {
                            //穿插人员查询
                            updateAllUser(line.getRefOrganizationCode(), departHalfList);

                        }
                    }
                }
            }
        }
    }

    private void dealFirstLine(List<OrgLine> allOrgLine) {
        for (int i = 0; i < allOrgLine.size(); i++) {
            OrgLine orgLine = allOrgLine.get(i);
            LxDepartVo lxDepartVo = new LxDepartVo();
            lxDepartVo.setSort(i + 1);
            if ("省司法厅".equals(orgLine.getOrganizationName())) {
                lxDepartVo.setId(orgLine.getRefOrganizationCode());
            } else {
                lxDepartVo.setId(orgLine.getOrganizationCode());
            }
            lxDepartVo.setParentId(orgLine.getParentCode());
            lxDepartVo.setName(orgLine.getOrganizationName());
            lxDepartVo.setType(OrganizationTypeEnum.getEnumByCode(orgLine.getType()).getType());
            if (!"A".equals(orgLine.getStatus())) {
                lxDepartVo.setOperatorType(2);
            }
            UnifyDepartNew departNew = unifyDepartNewService.getById(orgLine.getOrganizationCode());
            if (ObjectUtil.isNotEmpty(departNew)) {
                lxDepartVo.setOperatorType(1);
                LxDepartVo departVo = JSONObject.parseObject(departNew.getJsonInfo(), LxDepartVo.class);
                if (!lxDepartVo.equals(departVo) && ObjectUtil.isNotEmpty(lxDepartVo.getId())) {
                    unifyPushService.handleDepart(lxDepartVo);
                }
            } else {
                lxDepartVo.setOperatorType(0);
                if (ObjectUtil.isNotEmpty(lxDepartVo.getId())) {
                    unifyPushService.handleDepart(lxDepartVo);
                }
            }
        }
    }

    private void dealSecondLine(List<OrgLine> allOrgLine) {
        for (int i = 0; i < allOrgLine.size(); i++) {
            OrgLine orgLine = allOrgLine.get(i);
            LxDepartVo lxDepartVo = new LxDepartVo();
            lxDepartVo.setSort(i + 1);
            lxDepartVo.setId(orgLine.getRefOrganizationCode());
            lxDepartVo.setParentId(orgLine.getParentCode());
            lxDepartVo.setName(orgLine.getOrganizationName());
            lxDepartVo.setType(OrganizationTypeEnum.getEnumByCode(orgLine.getType()).getType());
            if (!"A".equals(orgLine.getStatus())) {
                lxDepartVo.setOperatorType(2);
            }
            UnifyDepartNew departNew = unifyDepartNewService.getById(orgLine.getOrganizationCode());
            if (ObjectUtil.isNotEmpty(departNew)) {
                lxDepartVo.setOperatorType(1);
                LxDepartVo departVo = JSONObject.parseObject(departNew.getJsonInfo(), LxDepartVo.class);
                if (!lxDepartVo.equals(departVo) && ObjectUtil.isNotEmpty(lxDepartVo.getId())) {
                    unifyPushService.handleDepart(lxDepartVo);
                }
            } else {
                lxDepartVo.setOperatorType(0);
                if (ObjectUtil.isNotEmpty(lxDepartVo.getId())) {
                    unifyPushService.handleDepart(lxDepartVo);
                }
            }
        }
    }

    /**
     * 获取条线机构
     *
     * @param domainCode
     * @param organizationCode
     * @return
     */
    public List<OrgLine> getAllOrgLine(String domainCode, String organizationCode) {
        List<OrgLine> orgLineList = new ArrayList<>();
        OapiMoziStripLineAddressOrgPageSubOrgsResponse response = ZwddUtil.pageSubOrgs(1, domainCode, organizationCode);
        if (ObjectUtil.isNotEmpty(response.getContent()) && ObjectUtil.isNotEmpty(response.getContent().getData())) {
            String data = response.getContent().getData();
            List<OrgLine> lineList = JSONObject.parseArray(data, OrgLine.class);
            orgLineList.addAll(lineList);
            Long totalSize = response.getContent().getTotalSize();
            if (totalSize > 100) {
                long t = totalSize / 2;
                for (long l = 0; l < t + 1; l++) {
                    OapiMoziStripLineAddressOrgPageSubOrgsResponse responsePage = ZwddUtil.pageSubOrgs((int) (l + 2), domainCode, organizationCode);
                    if (ObjectUtil.isNotEmpty(responsePage.getContent()) && ObjectUtil.isNotEmpty(responsePage.getContent().getData())) {
                        String dataPage = responsePage.getContent().getData();
                        List<OrgLine> lineListPage = JSONObject.parseArray(dataPage, OrgLine.class);
                        orgLineList.addAll(lineListPage);
                    }
                }
            }
        }
        return orgLineList;
    }

    /**
     * 根据code获取所有下级组织
     *
     * @param organizationCode
     * @return
     */
    public List<Organization> getAllOrganization(String organizationCode) {
        List<String> orgIds = new ArrayList<>();
        List<Organization> organizationList = new ArrayList<>();
        OapiMoziOrganizationPageSubOrganizationCodesResponse response = ZwddUtil.pageSubOrganizationCodes(1, organizationCode);
        if (response.getContent() != null) {
            Long totalSize = response.getContent().getTotalSize();
            String data = response.getContent().getData();
            List<String> stringList = JSONObject.parseArray(data, String.class);
            if (CollectionUtil.isNotEmpty(stringList)) {
                orgIds.addAll(stringList);
                if (totalSize > 100) {
                    int t = (int) (totalSize / 2);
                    for (int l = 0; l < t + 1; l++) {
                        OapiMoziOrganizationPageSubOrganizationCodesResponse responsePage = ZwddUtil.pageSubOrganizationCodes(l + 2, organizationCode);
                        if (responsePage.getContent() != null && responsePage.getContent().getData() != null) {
                            String dataPage = responsePage.getContent().getData();
                            List<String> lineListPage = JSONObject.parseArray(dataPage, String.class);
                            orgIds.addAll(lineListPage);
                        }
                    }
                }
            }

        }
        if (!orgIds.isEmpty()) {
            int size = orgIds.size();
            if (size > 100) {
                int j = (size / 100) + 1;
                for (int i = 0; i < j; i++) {
                    List<Organization> organizationsByCodes = ZwddUtil.listOrganizationsByCodes(orgIds.subList(100 * i, Math.min(100 * (i + 1), size)));
                    if (!organizationsByCodes.isEmpty()) {
                        organizationList.addAll(organizationsByCodes);
                        for (Organization organizationsByCode : organizationsByCodes) {
                            if (!organizationsByCode.isLeaf()) {
                                List<Organization> childList = getAllOrganization(organizationsByCode.getOrganizationCode());
                                if (CollectionUtil.isNotEmpty(childList)) {
                                    organizationList.addAll(childList);
                                }
                            }
                        }
                    }
                }
            } else {
                List<Organization> organizationsByCodes = ZwddUtil.listOrganizationsByCodes(orgIds);
                if (!organizationsByCodes.isEmpty()) {
                    for (Organization organizationsByCode : organizationsByCodes) {
                        //此处排除了省司法厅下属单位及下面的机构，这里2万人，数据太多，暂不同步
                        if (!organizationsByCode.isLeaf()) {
                            organizationList.add(organizationsByCode);
                            List<Organization> childList = getAllOrganization(organizationsByCode.getOrganizationCode());
                            if (CollectionUtil.isNotEmpty(childList)) {
                                organizationList.addAll(childList);
                            }
                        }else {
                            organizationList.add(organizationsByCode);
                        }
                    }
                }
            }
        }
        return organizationList;
    }

    public List<Organization> getAllOrganizationByList(List<String> orgIds) {
        List<Organization> organizationList = new ArrayList<>();
        int size = orgIds.size();
        if (size > 100) {
            int j = (size / 100) + 1;
            for (int i = 0; i < j; i++) {
                List<Organization> organizationsByCodes = ZwddUtil.listOrganizationsByCodes(orgIds.subList(100 * i, Math.min(100 * (i + 1), size)));
                if (!organizationsByCodes.isEmpty()) {
                    organizationList.addAll(organizationsByCodes);
                    for (Organization organizationsByCode : organizationsByCodes) {
                        if (!organizationsByCode.isLeaf()) {
                            List<Organization> childList = getAllOrganization(organizationsByCode.getOrganizationCode());
                            if (CollectionUtil.isNotEmpty(childList)) {
                                organizationList.addAll(childList);
                            }
                        }
                    }
                }
            }
        } else {
            List<Organization> organizationsByCodes = ZwddUtil.listOrganizationsByCodes(orgIds);
            if (!organizationsByCodes.isEmpty()) {
                organizationList.addAll(organizationsByCodes);
                for (Organization organizationsByCode : organizationsByCodes) {
                    if (!organizationsByCode.isLeaf()) {
                        List<Organization> childList = getAllOrganization(organizationsByCode.getOrganizationCode());
                        if (CollectionUtil.isNotEmpty(childList)) {
                            organizationList.addAll(childList);
                        }
                    }
                }
            }
        }
        return organizationList;
    }

    public void dealOrganization(List<Organization> organizationList) {
        for (Organization organization : organizationList) {
            LxDepartVo lxDepartVo = new LxDepartVo();
            lxDepartVo.setId(organization.getOrganizationCode());
            lxDepartVo.setSort(organization.getDisplayOrder());
            lxDepartVo.setParentId(organization.getParentCode());
            lxDepartVo.setName(organization.getOrganizationName());
            lxDepartVo.setType(OrganizationTypeEnum.getEnumByCode(organization.getTypeCode()).getType());
            lxDepartVo.setLeaf(organization.isLeaf());
            if ("F".equals(organization.getStatus())) {
                lxDepartVo.setOperatorType(2);
            }
            UnifyDepartNew departNew = unifyDepartNewService.getById(organization.getOrganizationCode());
            if (ObjectUtil.isNotEmpty(departNew)) {
                lxDepartVo.setOperatorType(1);
                LxDepartVo departVo = JSONObject.parseObject(departNew.getJsonInfo(), LxDepartVo.class);
                if (!lxDepartVo.equals(departVo)) {
                    unifyPushService.handleDepart(lxDepartVo);
                    unifyPushService.handleDepartSync(lxDepartVo, System.currentTimeMillis());
                }
            } else {
                lxDepartVo.setOperatorType(0);
                unifyPushService.handleDepart(lxDepartVo);
                unifyPushService.handleDepartSync(lxDepartVo, System.currentTimeMillis());
            }
        }

    }

    @Async
    @Override
    public void updateAllUser(String organizationCode, List<UnifyDepartHalf> unifyDepartHalfList) {
        OapiMoziOrganizationGetOrganizationEmployeeCountResponse countResponse = ZwddUtil.getOrganizationEmployeeCount(organizationCode);
        OapiSpResultContent content = countResponse.getContent();
        if (content != null) {
            String data = content.getData();
            if (data != null) {
                int personCount = Integer.parseInt(data);
                //如果人数小于1万，直接调用接口获取下面的所有人
                if (personCount < 10000) {
                    // 收集所有在职用户的 userid 列表
                    List<String> activeUserIds = new ArrayList<>();

                    int pageTotal = personCount / 100 + 1;
                    for (int i = 0; i < pageTotal; i++) {
                        OapiMoziOrganizationPageOrganizationEmployeePositionsResponse response = ZwddUtil.pageOrganizationEmployeePositions(i + 1, organizationCode);
                        if (response.getContent() != null) {
                            String userData = response.getContent().getData();
                            List<EmployeeInfo> employeeInfos = JSONObject.parseArray(userData, EmployeeInfo.class);
                            if (CollectionUtil.isNotEmpty(employeeInfos)) {
                                for (EmployeeInfo employeeInfo : employeeInfos) {
                                    // 收集在职用户ID
                                    activeUserIds.add(employeeInfo.getEmployeeCode());

                                    UnifyUserNew unifyUserNew = unifyUserNewService.getById(employeeInfo.getEmployeeCode());
                                    LxUserVo lxUserVo = userInfoBuild(organizationCode, employeeInfo, unifyUserNew, unifyDepartHalfList);
                                    if (ObjectUtil.isNotEmpty(unifyUserNew)) {
                                        LxUserVo userVo = JSONObject.parseObject(unifyUserNew.getJsonInfo(), LxUserVo.class);
                                        if (!lxUserVo.equals(userVo)) {
                                            log.debug("前后不一致：" + JSONObject.toJSONString(lxUserVo) + JSONObject.toJSONString(userVo));
                                            unifyPushService.handleUser(lxUserVo);
                                            unifyPushService.handleUserSync(lxUserVo, System.currentTimeMillis());
                                        }
                                    } else {
                                        unifyPushService.handleUser(lxUserVo);
                                        unifyPushService.handleUserSync(lxUserVo, System.currentTimeMillis());
                                    }
                                }
                            }
                            System.out.println(employeeInfos);
                        }
                    }

                    // 处理完所有在职用户后，清理不存在的用户数据
                    cleanupInactiveUsers(organizationCode, activeUserIds);
                }
            }

            log.debug(JSONObject.toJSONString(countResponse.getContent().getData()));
        }
    }

    /**
     * 清理不存在的用户数据
     *
     * @param organizationCode 组织代码
     * @param activeUserIds 在职用户ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    private void cleanupInactiveUsers(String organizationCode, List<String> activeUserIds) {
        try {
            log.info("开始清理组织 {} 中不存在的用户数据，在职用户数量: {}", organizationCode, activeUserIds.size());

            // 1. 查询当前组织相关的所有 UnifyUserNewExtendInfo 数据
            List<UnifyUserNewExtendInfo> allExtendInfos = unifyUserNewExtendInfoService.list(
                new QueryWrapper<UnifyUserNewExtendInfo>().lambda()
                    .eq(UnifyUserNewExtendInfo::getUserDeptId, organizationCode)
            );

            if (CollectionUtil.isEmpty(allExtendInfos)) {
                log.info("组织 {} 中没有找到扩展信息数据，跳过清理", organizationCode);
                return;
            }

            log.info("组织 {} 中找到 {} 条扩展信息数据", organizationCode, allExtendInfos.size());

            // 2. 找出在 UnifyUserNewExtendInfo 中存在但不在 activeUserIds 中的用户
            List<String> inactiveUserIds = allExtendInfos.stream()
                .map(UnifyUserNewExtendInfo::getUserId)
                .filter(userId -> !activeUserIds.contains(userId))
                .distinct()
                .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(inactiveUserIds)) {
                log.info("组织 {} 中没有找到需要清理的不活跃用户", organizationCode);
                return;
            }

            log.info("组织 {} 中找到 {} 个需要清理的不活跃用户: {}", organizationCode, inactiveUserIds.size(), inactiveUserIds);

            // 3. 删除 UnifyUserNewExtendInfo 表中这些不存在的用户数据
            int deletedExtendInfoCount = 0;
            for (String inactiveUserId : inactiveUserIds) {
                int deleted = unifyUserNewExtendInfoService.remove(
                    new QueryWrapper<UnifyUserNewExtendInfo>().lambda()
                        .eq(UnifyUserNewExtendInfo::getUserId, inactiveUserId)
                        .eq(UnifyUserNewExtendInfo::getUserDeptId, organizationCode)
                ) ? 1 : 0;
                deletedExtendInfoCount += deleted;

                if (deleted > 0) {
                    log.debug("删除用户 {} 在组织 {} 中的扩展信息", inactiveUserId, organizationCode);
                }
            }

            log.info("成功删除 {} 条扩展信息记录", deletedExtendInfoCount);

            // 4. 重新处理和更新 unify_user_new 表的相关数据
            updateUserNewDataAfterCleanup(inactiveUserIds);

            // 5. 将更新后的数据推送到所有相关的子系统
            pushUpdatedDataToSystems(inactiveUserIds);

            log.info("组织 {} 的用户数据清理完成", organizationCode);

        } catch (Exception e) {
            log.error("清理组织 {} 中不存在的用户数据时发生异常", organizationCode, e);
            throw e;
        }
    }

    private LxUserVo userInfoBuild(String organizationCode, EmployeeInfo employeeInfo, UnifyUserNew unifyUserNew, List<UnifyDepartHalf> unifyDepartHalfList) {
        LxUserVo lxUserVo = new LxUserVo();
        lxUserVo.setId(employeeInfo.getEmployeeCode());
        lxUserVo.setSex(Integer.parseInt(employeeInfo.getEmpGender()));
        lxUserVo.setName(employeeInfo.getEmployeeName());
        OapiMoziFusionPageSearchEmployeeResponse response = ZwddUtil.pageSearchEmployee(1, organizationCode, employeeInfo.getEmployeeName());
        OapiMoziEmployeeListEmployeePositionsByEmployeeCodeResponse employeeResponse = ZwddUtil.listEmployeePositionsByEmployeeCode(unifyUserNew.getId());

        if (response.getContent() != null) {
            String data = response.getContent().getData();
            if (data != null) {
                JSONArray jsonArray = JSONObject.parseArray(data);
                for (Object jsonString : jsonArray) {
                    JSONObject jsonObject = JSONObject.parseObject(jsonString.toString());
                    String accountId = jsonObject.getString("accountId");
                    String account = jsonObject.getString("account");
                    lxUserVo.setUserName(account);
                    lxUserVo.setAccountId(accountId);
                    break;
                }

            }
        }
        if (!"A".equals(employeeInfo.getStatus())) {
            lxUserVo.setOperatorType(2);
        } else {
            if (ObjectUtil.isNotEmpty(unifyUserNew)) {
                lxUserVo.setOperatorType(1);
            } else {
                lxUserVo.setOperatorType(0);
            }
        }
        List<EmployeePosition> positionList = new ArrayList<>();
        List<LxExtendInfosVo> lxExtendInfosVoList = new ArrayList<>();
        if (employeeResponse.getContent() != null) {
            String data = employeeResponse.getContent().getData();
            List<EmployeePosition> employeePositions = JSONObject.parseArray(data, EmployeePosition.class);
            if (CollectionUtil.isNotEmpty(employeePositions)) {
                positionList.addAll(employeePositions);
            }
        } else {
            positionList = employeeInfo.getGovEmployeePositions();
        }

        for (EmployeePosition employeePosition : positionList) {
            LxExtendInfosVo lxExtendInfosVo = new LxExtendInfosVo();
            lxExtendInfosVo.setUserId(employeePosition.getEmployeeCode());
            lxExtendInfosVo.setPosition(employeePosition.getGovEmpPosJob());
            lxExtendInfosVo.setRank(employeePosition.getVisibilityIndicatorCode());
            lxExtendInfosVo.setUserDeptId(employeePosition.getOrganizationCode());
            lxExtendInfosVo.setWorkPhone(employeePosition.getGovEmpPosPhoneNo());
            lxExtendInfosVo.setWorkAddress(employeePosition.getGovEmpPosAddress());
            lxExtendInfosVo.setSorts(employeePosition.getOrderInOrganization());
            UnifyDepartNew departNew = unifyDepartNewService.getById(employeePosition.getOrganizationCode());
            if (ObjectUtil.isNotEmpty(departNew)) {
                lxExtendInfosVo.setUserDeptName(departNew.getName());
            }
            Optional<UnifyDepartHalf> halfOptional = unifyDepartHalfList.stream().filter(e -> e.getOrganizationUuid().equals(lxExtendInfosVo.getUserDeptId())).findFirst();
            if (halfOptional.isPresent()) {
                UnifyDepartHalf unifyDepartHalf = halfOptional.get();
                lxExtendInfosVo.setJzjg(unifyDepartHalf.getOldOrgId());
                lxExtendInfosVo.setJzjgName(unifyDepartHalf.getOldOrgName());
            }
            lxExtendInfosVoList.add(lxExtendInfosVo);
        }
        if (CollectionUtil.isNotEmpty(lxExtendInfosVoList)) {
            List<String> collect = lxExtendInfosVoList.stream().map(LxExtendInfosVo::getUserDeptId).collect(Collectors.toList());
            lxUserVo.setDeptId(String.join(",", collect));
        }
        //补充手机号
        String phone = lxUserVo.getPhone();

        if (ObjectUtil.isNotNull(unifyUserNew)) {
            UnifyUser unifyUser = unifyUserService.getById(unifyUserNew.getId());
            if (ObjectUtil.isNotEmpty(unifyUser)) {
                phone = unifyUser.getPhoneNumbers();
            } else if (ObjectUtil.isNotEmpty(unifyUserNew.getPhone())) {
                phone = unifyUserNew.getPhone();
            }

        }
        lxUserVo.setPhone(phone);
        lxUserVo.setExtendInfos(lxExtendInfosVoList);
        return lxUserVo;
    }

    @Override
    @Cacheable(value = "testCache")
    public String testCache(String number) {
        return "123";
    }

    @Override
    @CacheEvict(value = "testCache")
    public void resetCache(String number) {
    }

    /**
     * 清理后更新 unify_user_new 表的相关数据
     *
     * @param inactiveUserIds 不活跃用户ID列表
     */
    private void updateUserNewDataAfterCleanup(List<String> inactiveUserIds) {
        try {
            log.info("开始更新 {} 个用户的 unify_user_new 数据", inactiveUserIds.size());

            for (String userId : inactiveUserIds) {
                UnifyUserNew unifyUserNew = unifyUserNewService.getById(userId);
                if (ObjectUtil.isNotEmpty(unifyUserNew)) {
                    // 重新查询该用户的所有扩展信息
                    List<UnifyUserNewExtendInfo> remainingExtendInfos = unifyUserNewExtendInfoService.list(
                        new QueryWrapper<UnifyUserNewExtendInfo>().lambda()
                            .eq(UnifyUserNewExtendInfo::getUserId, userId)
                    );

                    // 重新构建用户的 JSON 信息
                    String jsonInfo = unifyUserNew.getJsonInfo();
                    if (ObjectUtil.isNotEmpty(jsonInfo)) {
                        LxUserVo lxUserVo = JSONObject.parseObject(jsonInfo, LxUserVo.class);

                        // 更新扩展信息
                        if (CollectionUtil.isNotEmpty(remainingExtendInfos)) {
                            List<LxExtendInfosVo> lxExtendInfosVos = new ArrayList<>();
                            for (UnifyUserNewExtendInfo extendInfo : remainingExtendInfos) {
                                LxExtendInfosVo lxExtendInfosVo = new LxExtendInfosVo();
                                lxExtendInfosVo.setUserId(extendInfo.getUserId());
                                lxExtendInfosVo.setPosition(extendInfo.getPosition());
                                lxExtendInfosVo.setRank(extendInfo.getRank());
                                lxExtendInfosVo.setUserDeptId(extendInfo.getUserDeptId());
                                lxExtendInfosVo.setUserDeptName(extendInfo.getUserDeptName());
                                lxExtendInfosVo.setWorkAddress(extendInfo.getWorkAddress());
                                lxExtendInfosVo.setWorkPhone(extendInfo.getWorkPhone());
                                lxExtendInfosVo.setJzjg(extendInfo.getJzjg());
                                lxExtendInfosVo.setJzjgName(extendInfo.getJzjgName());
                                lxExtendInfosVo.setSorts(extendInfo.getSorts());
                                lxExtendInfosVos.add(lxExtendInfosVo);
                            }
                            lxUserVo.setExtendInfos(lxExtendInfosVos);

                            // 更新部门ID
                            List<String> deptIds = lxExtendInfosVos.stream()
                                .map(LxExtendInfosVo::getUserDeptId)
                                .filter(ObjectUtil::isNotEmpty)
                                .collect(Collectors.toList());
                            lxUserVo.setDeptId(String.join(",", deptIds));
                        } else {
                            // 如果没有剩余的扩展信息，清空相关字段
                            lxUserVo.setExtendInfos(new ArrayList<>());
                            lxUserVo.setDeptId("");
                        }

                        // 更新 JSON 信息
                        unifyUserNew.setJsonInfo(JSONObject.toJSONString(lxUserVo));
                        unifyUserNewService.updateById(unifyUserNew);

                        log.debug("更新用户 {} 的 unify_user_new 数据", userId);
                    }
                }
            }

            log.info("完成 {} 个用户的 unify_user_new 数据更新", inactiveUserIds.size());

        } catch (Exception e) {
            log.error("更新 unify_user_new 数据时发生异常", e);
            throw e;
        }
    }

    /**
     * 将更新后的数据推送到所有相关的子系统
     *
     * @param inactiveUserIds 不活跃用户ID列表
     */
    private void pushUpdatedDataToSystems(List<String> inactiveUserIds) {
        try {
            log.info("开始推送 {} 个用户的更新数据到子系统", inactiveUserIds.size());

            for (String userId : inactiveUserIds) {
                UnifyUserNew unifyUserNew = unifyUserNewService.getById(userId);
                if (ObjectUtil.isNotEmpty(unifyUserNew) && ObjectUtil.isNotEmpty(unifyUserNew.getJsonInfo())) {
                    LxUserVo lxUserVo = JSONObject.parseObject(unifyUserNew.getJsonInfo(), LxUserVo.class);

                    // 设置操作类型为更新
                    lxUserVo.setOperatorType(1);

                    // 推送到统一推送服务
                    unifyPushService.handleUser(lxUserVo);
                    unifyPushService.handleUserSync(lxUserVo, System.currentTimeMillis());

                    log.debug("推送用户 {} 的更新数据到子系统", userId);
                }
            }

            log.info("完成 {} 个用户的数据推送", inactiveUserIds.size());

        } catch (Exception e) {
            log.error("推送更新数据到子系统时发生异常", e);
            throw e;
        }
    }
}
